{"app": {"title": "Alerte CoMapeo", "mapAlertSystem": "Système d'alerte cartographique"}, "auth": {"title": "Système d'alerte cartographique", "subtitle": "Entrez vos identifiants serveur pour continuer", "serverName": "Nom du serveur", "serverNamePlaceholder": "Entrez l'URL du serveur", "bearerToken": "<PERSON><PERSON>", "bearerTokenPlaceholder": "Entrez votre token bearer", "rememberMe": "Se souvenir de moi", "connect": "Se connecter", "connecting": "Connexion en cours...", "successfullyAuthenticated": "Authentification réussie", "loggedOutSuccessfully": "Déconnecté avec succès"}, "map": {"searchPlaceholder": "Rechercher un lieu...", "searchTip": "Recherchez n'importe quel endroit dans le monde avec la géocodification Mapbox", "recentSearches": "Recherches récentes :", "selectedLocation": "Emplacement sélectionné", "continue": "<PERSON><PERSON><PERSON>", "manualEntry": "<PERSON><PERSON>", "tapToSelect": "Appuyez n'importe où sur la carte pour sélectionner des coordonnées", "locationSelected": "Emplacement sélectionné : {{lat}}, {{lng}}", "locationFound": "{{query}} trouvé : {{lat}}, {{lng}}", "locationNotFound": "Emplacement non trouvé. Veuillez essayer une autre recherche.", "searchFailed": "Échec de la recherche. Vérifiez votre connexion et réessayez.", "mapConfigError": "Erreur de configuration de la carte. Veuillez contacter le support.", "loadingMap": "Chargement de la carte...", "coordinatesCopied": "Coordonnées copiées dans le presse-papiers", "failedToCopy": "Échec de la copie des coordonnées", "coordinatesSetManually": "Coordonnées configurées manuellement : {{lat}}, {{lng}}", "pleaseSelectCoordinates": "Veuillez d'abord sélectionner des coordonnées"}, "mapbox": {"title": "Configuration Mapbox", "token": "Token public Mapbox", "tokenPlaceholder": "pk.********************************...", "getTokenFrom": "Obtenez votre token de", "initializeMap": "Initialiser la carte", "backToLogin": "Retour à la connexion", "tokenSetSuccessfully": "Token Mapbox configuré avec succès", "enterValidToken": "Veuillez entrer un token Mapbox valide"}, "manualCoords": {"title": "Coordonnées manuelles", "latitude": "Latitude (-90 à 90)", "longitude": "Longitude (-180 à 180)", "latitudePlaceholder": "51.5074", "longitudePlaceholder": "-0.1278", "setCoordinates": "Définir les coordonnées", "invalidCoordinates": "Veuillez entrer des coordonnées valides (lat : -90 à 90, lng : -180 à 180)"}, "projects": {"title": "Sélectionner les projets", "subtitle": "Choisissez vers quels projets envoyer l'alerte", "loadingProjects": "Chargement des projets...", "noProjectsTitle": "Aucune projet disponible", "noProjectsMessage": "Aucun projet disponible pour votre compte. Veuillez contacter votre administrateur pour y accéder.", "backToMap": "Retour à la carte", "foundProjects": "{{count}} projet(s) trouvé(s)", "failedToFetch": "Échec de la récupération des projets", "pleaseSelectAtLeast": "Veuillez sélectionner au moins un projet", "continueToAlert": "Continuer vers le formulaire d'alerte ({{count}} sélectionné(s))", "selected": "{{count}} projet{{plural}} sélectionné{{plural}} :", "logout": "Se déconnecter", "selectProject": "Sélectionner un projet", "noProjects": "Aucun projet"}, "alert": {"title": "<PERSON><PERSON><PERSON> une alerte", "subtitle": "Dé<PERSON> de l'alerte", "location": "Emplacement :", "selectedProjects": "Projets sélectionnés ({{count}}) :", "detectionStartTime": "Heure de début de détection", "detectionEndTime": "Heure de fin de détection", "sourceId": "ID de la source", "sourceIdPlaceholder": "Entrez l'identifiant de la source", "alertName": "Nom de l'alerte (format slug)", "alertNamePlaceholder": "nom-de-l-alerte", "slugFormatHelp": "Utilisez uniquement des lettres minuscules, des chiffres et des tirets", "invalidFormat": "Format invalide. Utilisez uniquement des minuscules, des chiffres et des tirets", "endTimeAfterStart": "L'heure de fin doit être après l'heure de début", "fillAllFields": "<PERSON><PERSON><PERSON>z remplir tous les champs", "slugFormatError": "Le nom de l'alerte doit être au format slug (minuscules, chiffres et tirets uniquement)", "submitAlert": "Soumettre l'alerte à {{count}} projet(s)", "creatingAlerts": "Création d'alertes en cours...", "alertCreatedSuccessfully": "Alerte c<PERSON>ée avec succès", "partiallyCompleted": "Partiellement terminé", "creationFailed": "Échec de la création", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "successMessage": "Alerte créée avec succès pour {{count}} projet{{plural}}", "partialMessage": "Alerte créée pour {{successCount}} projet{{successPlural}}, échec pour {{errorCount}}", "failedMessage": "Échec de la création d'une alerte pour un ou plusieurs projets", "unexpectedError": "Une erreur inattendue s'est produite", "copy": "<PERSON><PERSON><PERSON>", "back": "Retour"}, "alertPopup": {"title": "Dé<PERSON> de l'alerte", "close": "<PERSON><PERSON><PERSON> les détails de l'alerte", "alertName": "Nom de l'alerte", "project": "Projet", "coordinates": "Coordonnées", "detectionPeriod": "Période de détection", "start": "Début : {{date}}", "end": "Fin : {{date}}", "sourceId": "ID de la source", "na": "N/A", "invalidDate": "Date invalide"}, "common": {"install": "Installer", "installApp": "Installer l'application", "go": "<PERSON><PERSON>", "and": "et", "more": "plus"}, "language": {"switchLanguage": "Changer de langue", "english": "<PERSON><PERSON><PERSON>"}}